package handler

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	poolsdk "github.com/sourcegraph/conc/pool"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/base"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/codeassist"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	doubaoservice "code.byted.org/devgpt/kiwis/codeassist/context/service"
	journalentity "code.byted.org/devgpt/kiwis/codeassist/journal/entity"
	journalservice "code.byted.org/devgpt/kiwis/codeassist/journal/service"
	"code.byted.org/devgpt/kiwis/codeassist/sandbox/dal"
	sandboxentity "code.byted.org/devgpt/kiwis/codeassist/sandbox/entity"
	"code.byted.org/devgpt/kiwis/codeassist/sandbox/pack"
	sandboxservice "code.byted.org/devgpt/kiwis/codeassist/sandbox/service"
	"code.byted.org/devgpt/kiwis/codeassist/sandbox/templates"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/kitex"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/eventbus"
	redisCli "code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/overpass/common/option/calloption"
	"code.byted.org/overpass/ocean_cloud_review/kitex_gen/ocean/cloud/review"
	"code.byted.org/overpass/ocean_cloud_review/rpc/ocean_cloud_review"
)

const (
	// 返回内容过多，截断阈值
	imgNumLimit  = 50
	textNumLimit = 200
	// 最大并发数
	maxGoroutines = 10

	// 豆包Bot id
	doubaoBotID = "7338286299411103781"
	// 豆包App id，在samantha中定义
	doubaoAppID = 482431
	// source (打点使用)
	codeAssistSource = "pc_codeassist"
	// 审核ScenarioID
	textScenarioID = 200000000179
	imgScenarioID  = 200000000074
	// Tcc ocean.cloud.alice safety_link_switch_config response_review_len
	respReviewLen = 128
)

type SandboxHandler struct {
	ExecutableCheckService sandboxservice.ExecutableCheckService
	SandboxService         sandboxservice.SandboxManagerService
	DoubaoService          doubaoservice.DoubaoService
	JournalService         *journalservice.Service
	Config                 *config.CodeAssistAssistantConfig
	EventbusClient         eventbus.Client
	TemplateCache          *templates.TemplateCache
	SandboxAPIConfig       *tcc.GenericConfig[config.SandboxAPIConfig]
	ArtifactsBuildInfoDAO  dal.ArtifactsBuildInfoDAO
	ArtifactsFileInfoDAO   dal.ArtifactsFileInfoDAO
	RedisCli               redisCli.Client
}

func (h *SandboxHandler) RunCodeV2(ctx context.Context, req *codeassist.RunCodeRequestV2) (*codeassist.RunCodeResponseV2, error) {
	var (
		checkStartTime         time.Time
		checkTimeCost          int64
		err                    error
		language               string
		isExecutable           bool
		artifactGenerateStatus int64
		codeFiles              []*sandboxentity.CodeFile
		ret                    *sandboxentity.ExecuteCodeResult
		checkResults           []*sandboxentity.ExecutableResult
		notExecutableReason    = make([]*journalentity.FileIssue, 0)
		resp                   = &codeassist.RunCodeResponseV2{
			Data: &codeassist.RunCodeData{
				Result_:    &codeassist.RunCodeResult_{},
				IsTruncate: lo.ToPtr(false),
			},
			BaseResp: &base.BaseResp{},
		}
	)

	// 埋点上报
	defer func() {
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.V1.CtxError(ctx, "[RunCodeV2]"+
						" panic in walkthrough goroutine: %+v, stacktrace: %s", r, string(debug.Stack()))
				}
			}()
			var (
				identifier = ""
				version    = ""
				logID, _   = ctxvalues.LogID(ctx)
			)
			if req.ArtifactCodeInfo != nil {
				identifier = req.ArtifactCodeInfo.Identifier
				version = req.ArtifactCodeInfo.Version
			}
			h.sendCodeCheckResultEvent(ctx, sandboxentity.CheckCodeEventOption{
				UserID:       req.UserID,
				MessageID:    lo.FromPtr(req.MessageID),
				Language:     strings.ToLower(language),
				Identifier:   identifier,
				Version:      version,
				CodeType:     pack.GetCodeTypeFromDTO(req.CodeType),
				IsExecutable: isExecutable,
				NotExecutableCategory: choose.IfLazyL(len(checkResults) > 0, func() string {
					return checkResults[0].Category.String()
				}, ""),
				NotExecutableReason:    notExecutableReason,
				Time:                   checkTimeCost,
				ArtifactGenerateStatus: artifactGenerateStatus,
				LogID:                  logID,
			})
		}()
	}()

	// 获取代码
	group, _ := errgroup.WithContext(ctx)
	group.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "[RunCodeV2] Panic in fetchCodeStatusFromDoubao: %v\n", r)
				err = fmt.Errorf("[RunCodeV2] Panic in fetchCodeStatusFromDoubao: %v\n", r)
			}
		}()

		var err error
		artifactGenerateStatus, err = h.fetchCodeStatusFromDoubao(ctx, req.UserID, req.CodeType, req.ArtifactCodeInfo)
		return err
	})

	group.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "[RunCodeV2] Panic in GetSingleArtifactCodeFile: %v", r)
				err = fmt.Errorf("[RunCodeV2] Panic in GetSingleArtifactCodeFile: %v", r)
			}
		}()

		var err error
		codeFiles, err = h.fetchCodeFileFromDoubao(ctx, req.UserID, req.CodeType, req.ArtifactCodeInfo, req.RawCodeInfo)
		return err
	})

	// 等待所有goroutine完成
	if err := group.Wait(); err != nil {
		return nil, err
	}

	// 规则检查
	if len(codeFiles) != 1 {
		errorMsg := "more than one code file"
		resp.Code = conv.DefaultAnyPtr[int32](common.ErrorCode_ErrCodePreCheckFail)
		resp.Message = &errorMsg
		isExecutable = false
		return resp, kitex.NewKitexError(common.ErrorCode_ErrCodePreCheckFail,
			errors.New(errorMsg))
	}
	language = codeFiles[0].Language
	checkStartTime = time.Now()
	for _, file := range codeFiles {
		if file == nil {
			continue
		}
		isExecutable, checkResults = h.ExecutableCheckService.CheckCodeIsExecutable(ctx, &sandboxservice.CodeIsExecutableOption{
			CheckerType:  sandboxservice.CheckerTypeLanguageType,
			LanguageType: file.Language,
			Code:         file.Content,
		})
		for _, result := range checkResults {
			if result == nil {
				continue
			}
			logs.V1.CtxWarn(ctx, "[RunCodeV2] refuse to run the code, file: %s, result: %v", file.Path, result.String())
			notExecutableReason = append(notExecutableReason, &journalentity.FileIssue{
				FilePath: file.Path,
				Issue:    result.Issue,
				Category: result.Category.String(),
			})
		}
		if !isExecutable {
			resp.Code = conv.DefaultAnyPtr[int32](common.ErrorCode_ErrCodePreCheckFail)
			errorMsg := "failed to check if the code is executable"
			if len(notExecutableReason) > 0 && notExecutableReason[0] != nil {
				errorMsg = notExecutableReason[0].Issue
			}
			resp.Message = &errorMsg
			return resp, kitex.NewKitexError(common.ErrorCode_ErrCodePreCheckFail, errors.New(errorMsg))
		}
	}
	checkTimeCost = time.Since(checkStartTime).Milliseconds()

	// 执行代码
	opt := &sandboxentity.ExecuteCodeOption{
		UID:                    req.UserID,
		MessageID:              *req.MessageID,
		LanguageType:           language,
		CodeFiles:              codeFiles,
		CodeType:               string(choose.If(req.CodeType == 1, sandboxentity.CodeTypeArtifacts, choose.If(req.CodeType == 2, sandboxentity.CodeTypeRaw, sandboxentity.CodeTypeUnknown))),
		ArtifactGenerateStatus: artifactGenerateStatus,
	}
	ret, err = h.RunTypeExecuteCode(ctx, opt)
	if err != nil {
		logs.CtxError(ctx, "[RunCodeV2] execute code failed, err: %v", err)
		return nil, sandboxservice.ErrorToKiteXError(err)
	}
	err = h.processRunCodeResult(ctx, ret, req, resp)
	if err != nil {
		return resp, sandboxservice.ErrorToKiteXError(err)
	}
	return resp, nil
}

func (h *SandboxHandler) RunTypeExecuteCode(ctx context.Context, opt *sandboxentity.ExecuteCodeOption) (*sandboxentity.ExecuteCodeResult, error) {
	if len(opt.CodeFiles) < 1 {
		return nil, &sandboxservice.StatusCodeError{StatusCode: sandboxservice.ErrCodeSandboxCodeNotSupport, Message: "no code files"}
	}
	sandbox, err := h.SandboxService.CreateSandbox(ctx, &sandboxservice.CreateSandboxOpt{
		AllocationID: strconv.FormatInt(opt.UID, 10),
		SessionID:    strconv.FormatInt(opt.UID, 10),
		FunctionType: sandboxentity.FunctionTypeRun,
		SandboxType:  sandboxentity.SandboxTypeExclusive,
		Extension:    false,
		AliveTime:    lo.ToPtr(int64(sandboxentity.SandboxActionDefaultTime)),
		Language:     lo.ToPtr(opt.LanguageType),
	})
	if err != nil {
		return nil, sandboxservice.ErrorToKiteXError(err)
	}
	res, err := h.SandboxService.ExecuteCode(ctx, sandboxentity.SandboxIdentifier{
		AllocationID: sandbox.AllocationID,
		SessionID:    sandbox.SessionID,
		FunctionType: sandbox.FunctionType,
		SandboxType:  sandbox.SandboxType,
	}, &sandboxservice.ExecuteOpt{
		FunctionID:   sandbox.FunctionID,
		InstanceName: sandbox.InstanceName,
		ExecuteDetail: &sandboxservice.ExecuteDetail{
			Code:         &opt.CodeFiles[0].Content,
			Language:     opt.LanguageType,
			ShareContext: false,
		},
		ExecuteMetricsDetail: sandboxservice.ExecuteMetricsDetail{
			UserID:                 opt.UID,
			MessageID:              opt.MessageID,
			Identifier:             opt.Identifier,
			Version:                opt.Version,
			CodeID:                 opt.CodeID,
			CodeVersion:            opt.CodeVersion,
			CodeType:               sandboxentity.CodeType(opt.CodeType),
			ArtifactGenerateStatus: opt.ArtifactGenerateStatus,
		},
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[RunTypeExecuteCode] execute code failed, err: %v", err)
		return nil, err
	}
	resp := h.SandboxResp2ServiceResp(ctx, res)
	if resp.Message == sandboxentity.CommandExecuteTimeLimitExceeded {
		return nil, &sandboxservice.StatusCodeError{
			StatusCode: sandboxservice.ErrCodeSandboxCodeRunningTimeout,
		}
	}
	return resp, nil
}

func (h *SandboxHandler) SandboxResp2ServiceResp(ctx context.Context, resp *sandboxservice.ExecuteResponse) *sandboxentity.ExecuteCodeResult {
	logs.V1.CtxInfo(ctx, "[packSandboxResp2ServiceResp] inputParameter resp: %v", resp)
	result := &sandboxentity.ExecuteCodeResult{
		Code: 0,
	}
	if resp == nil {
		return result
	}

	if resp.Status == sandboxentity.ExecuteSuccess {
		resp.Message = sandboxentity.ExecuteSuccess
	} else if resp.Status == sandboxentity.ExecuteFailed {
		if h.hasTimeout(resp.CompileResult) || h.hasTimeout(resp.RunResult) {
			result.Code = int32(sandboxservice.ErrCodeSandboxCodeRunningTimeout)
			result.Data.IsPartial = true
			result.Message = sandboxentity.CommandExecuteTimeLimitExceeded
		} else {
			result.Message = sandboxentity.CommandExecuteReturnNonZeroCode
		}
	} else {
		result.Code = int32(sandboxservice.ErrCodeSandboxInternalError)
		result.Message = sandboxentity.ExecuteSandboxError
	}

	// 创建 ExecuteCodeSlice 用于存放输出结果
	var slice sandboxentity.ExecuteCodeSlice
	slice.CodeOutputResult = append(slice.CodeOutputResult, packExecuteResults(resp.CompileResult, sandboxentity.ExecuteStageCompile)...)
	slice.CodeOutputResult = append(slice.CodeOutputResult, packExecuteResults(resp.RunResult, sandboxentity.ExecuteStageExecute)...)
	result.Data.Result = slice

	logs.V1.CtxInfo(ctx, "[packSandboxResp2ServiceResp] [Result: %v]", result)
	return result
}

func (h *SandboxHandler) hasTimeout(results []*sandboxservice.ExecuteCommandResult) bool {
	for _, res := range results {
		if res.Status == sandboxentity.CommandExecuteTimeLimitExceeded {
			return true
		}
	}
	return false
}

func packExecuteResults(results []*sandboxservice.ExecuteCommandResult, stage string) []sandboxentity.ExecuteCodeOutputResult {
	var output []sandboxentity.ExecuteCodeOutputResult
	for _, res := range results {
		if res == nil {
			continue
		}
		reason := ""
		if res.ReturnCode != 0 {
			reason = "return code non-zero"
		}

		output = append(output, sandboxentity.ExecuteCodeOutputResult{
			Stage:      stage,
			Type:       string(res.Type),
			ReturnCode: res.ReturnCode,
			Content:    res.Content,
			Reason:     reason,
		})
	}
	return output
}

func (h *SandboxHandler) processRunCodeResult(ctx context.Context, ret *sandboxentity.ExecuteCodeResult,
	req *codeassist.RunCodeRequestV2, resp *codeassist.RunCodeResponseV2) error {
	resp.Message = &ret.Message
	resp.Code = &ret.Code
	resp.Data.IsPartial = &ret.Data.IsPartial

	tempTexts := lo.Filter(ret.Data.Result.CodeOutputResult, func(item sandboxentity.ExecuteCodeOutputResult, _ int) bool {
		return item.Type == sandboxentity.RunCodeRetTypeStdout
	})
	// 将返回的一行内容拆分为多行
	texts := lo.FlatMap(tempTexts, func(item sandboxentity.ExecuteCodeOutputResult, _ int) []sandboxentity.ExecuteCodeOutputResult {
		tempItems := make([]sandboxentity.ExecuteCodeOutputResult, 0)
		parts := strings.Split(item.Content, "\n")
		for _, v := range parts {
			if v != "" {
				tempItems = append(tempItems, sandboxentity.ExecuteCodeOutputResult{
					Type:    item.Type,
					Content: v,
					Reason:  item.Reason,
				})
			}
		}
		return tempItems
	})
	images := lo.Filter(ret.Data.Result.CodeOutputResult, func(item sandboxentity.ExecuteCodeOutputResult, index int) bool {
		return item.Type == sandboxentity.RunCodeRetTypeImg
	})

	// 当输出过多时，对内容进行截断
	if len(texts) > textNumLimit {
		resp.Data.IsTruncate = lo.ToPtr(true)
		texts = texts[:textNumLimit]
	}

	if len(images) > imgNumLimit {
		resp.Data.IsTruncate = lo.ToPtr(true)
		images = images[:imgNumLimit]
	}

	// 所有文字拼接在一起进行审核处理
	allTexts := lo.Reduce(texts, func(agg string, item sandboxentity.ExecuteCodeOutputResult, _ int) string {
		return agg + item.Content
	}, "")

	if allTexts != "" {
		// 考虑到审核流程可能有超时情况产生，因此切分文本后，并发请求审核接口，暂定并发度为maxGoroutines
		splitTexts := slidingSplit(allTexts, respReviewLen)

		var mu sync.Mutex
		reviewResults := make([]review.CheckResultEnum, 0)
		parallelPool := poolsdk.New().WithMaxGoroutines(maxGoroutines).WithErrors()

		for _, v := range splitTexts {
			tmpText := v
			parallelPool.Go(func() error {
				param := &sandboxentity.BizParam{
					Texts:    []string{tmpText},
					Response: tmpText,
					BotID:    doubaoBotID,
					BotType:  strconv.Itoa(int(review.BotType_Coco)),
					Source:   codeAssistSource,
				}

				marshalTextsParam, err := json.Marshal(param)
				if err != nil {
					logs.CtxError(ctx, "marshal texts failed, err: %v", err)
					return errors.Errorf("marshal texts failed, err: %v", err)
				}
				strTextsParam := string(marshalTextsParam)

				// 调用审核服务（AsyncReview不是异步服务，此处为调用下游取名有点问题）
				textResp, err := ocean_cloud_review.RawCall.AsyncReview(ctx, &review.AsyncReviewRequest{
					GeneralReviewParam: &review.GeneralReviewRequestParam{
						ReviewEntityID:   conv.DefaultAny[int64](req.MessageID),
						AppID:            doubaoAppID,    // AppID_Doubao，和AppID_Cici区分开
						ReviewScenarioID: textScenarioID, // 文审
					},
					BizJSONParam: &strTextsParam,
				},
					calloption.WithReqRespLogsInfo(), calloption.WithRPCTimeout(time.Millisecond*5000))
				if err != nil {
					logs.CtxError(ctx, "review text failed, err: %v", err)
					return errors.Errorf("review text failed, err: %v", err)
				}
				// 审核不通过时，考虑使用其他code进行标识，方便前端进行展示
				resultOp := &sandboxentity.ResultOperation{}
				err = json.Unmarshal([]byte(textResp.ResultOperation), resultOp)
				if err != nil {
					logs.CtxError(ctx, "unmarshal result operation err:%v", err)
					return errors.Errorf("unmarshal result operation err:%v", err)
				}

				if len(resultOp.Ops) > 0 {
					logs.CtxError(ctx, "review text not pass, result: %v", textResp.GetCheckResult_())
					mu.Lock()
					reviewResults = append(reviewResults, textResp.GetCheckResult_())
					mu.Unlock()
				}

				return nil
			})
		}
		err := parallelPool.Wait()

		// 调用下游异常
		if err != nil {
			return kitex.NewKitexError(common.ErrorCode_ErrInternal, errors.Errorf("failed to wait for all goroutines to finish, err is %+v", err))
		}

		// 审核出现敏感词
		if len(reviewResults) > 0 {
			resp.BaseResp.StatusCode = int32(common.ErrorCode_ErrSecurityCheckFail)
			return kitex.NewKitexError(common.ErrorCode_ErrSecurityCheckFail, errors.Errorf("review text not pass"))
		}
	}

	imageURLs := make([]string, 0, len(images))
	if len(images) > 0 {
		var (
			mu            sync.Mutex
			reviewResults = make([]review.CheckResultEnum, 0)
			parallelPool  = poolsdk.New().WithMaxGoroutines(maxGoroutines).WithErrors()
		)

		for _, v := range images {
			tmpImg := v
			parallelPool.Go(func() error {
				imgData, err := base64.StdEncoding.DecodeString(tmpImg.Content)
				if err != nil {
					return errors.Errorf("decode image failed, err: %v", err)
				}
				uri, err := h.DoubaoService.StreamUploadSlice(ctx, imgData, "temp.png", req.UserID)
				if err != nil {
					return errors.Errorf("upload image failed, err: %v", err)
				}
				imgURL, _, err := h.DoubaoService.GetDoubaoURLByURI(ctx, uri, req.UserID)
				if err != nil {
					return errors.Errorf("get doubao url failed, err: %v", err)
				}

				mu.Lock()
				imageURLs = append(imageURLs, imgURL)
				mu.Unlock()

				startTime := time.Now()
				imageResp, err := ocean_cloud_review.ImageReview(ctx, &review.BaseReviewRequestParam{
					ReviewEntityId:   conv.DefaultAny[int64](req.MessageID),
					AppId:            doubaoAppID, // AppID_Doubao，和AppID_Cici区分开
					ReviewScenarioId: imgScenarioID,
				},
					imgURL, calloption.WithReqRespLogsInfo())

				if err != nil {
					logs.CtxError(ctx, "review img failed, err: %v", err)
					return errors.Errorf("review img failed, err: %v", err)
				}

				// 针对image review进行打点
				_ = metrics.CodeAssistMetric.ImageReviewResultLatency.WithTags(&metrics.CodeAssistImageReviewResultLatencyTag{
					Category: choose.IfLazyL(imageResp != nil, func() int {
						return int(imageResp.GetCheckResult_())
					}, 0),
				}).Observe(time.Since(startTime).Seconds())

				// 审核不通过时，考虑使用其他code进行标识，方便前端进行展示
				if imageResp.GetCheckResult_() != review.CheckResultEnum_Pass {
					logs.CtxError(ctx, "review img not pass, result: %v", imageResp.GetCheckResult_())
					mu.Lock()
					reviewResults = append(reviewResults, imageResp.GetCheckResult_())
					mu.Unlock()
				}
				return nil
			})
		}
		err := parallelPool.Wait()

		// 调用下游异常
		if err != nil {
			return kitex.NewKitexError(common.ErrorCode_ErrInternal, errors.Errorf("failed to wait for all goroutines to finish, err is %+v", err))
		}

		// 审核出现敏感词
		if len(reviewResults) > 0 {
			resp.BaseResp.StatusCode = int32(common.ErrorCode_ErrSecurityCheckFail)
			return kitex.NewKitexError(common.ErrorCode_ErrSecurityCheckFail, errors.Errorf("review img not pass"))
		}
	}

	// 由于需要展示根据换行符分割后的文本，因此这里使用texts填充即可。同时考虑到有reach limit的限制，因此图片/文本统一重新填充
	codeOutputResult := lo.FilterMap(ret.Data.Result.CodeOutputResult, func(item sandboxentity.ExecuteCodeOutputResult, index int) (*codeassist.RunCodeOutputResult_, bool) {
		if item.Type == sandboxentity.RunCodeRetTypeStderr {
			return &codeassist.RunCodeOutputResult_{
				Type:    &item.Type,
				Content: &item.Content,
				Reason:  &item.Reason,
			}, true
		}
		return nil, false
	})
	codeOutputResult = append(codeOutputResult, lo.Map(texts, func(item sandboxentity.ExecuteCodeOutputResult, index int) *codeassist.RunCodeOutputResult_ {
		return &codeassist.RunCodeOutputResult_{
			Type:    &item.Type,
			Content: &item.Content,
			Reason:  &item.Reason,
		}
	})...)
	codeOutputResult = append(codeOutputResult, lo.Map(images, func(item sandboxentity.ExecuteCodeOutputResult, index int) *codeassist.RunCodeOutputResult_ {
		return &codeassist.RunCodeOutputResult_{
			Type:    &item.Type,
			Content: &item.Content,
			Reason:  &item.Reason,
		}
	})...)

	resp.Data.Result_.CodeOutputResult_ = codeOutputResult
	return nil
}

func (h *SandboxHandler) sendCodeCheckResultEvent(ctx context.Context, opt sandboxentity.CheckCodeEventOption) {
	log.V1.CtxInfo(ctx, "[sendCodeCheckResultEvent] send run code v2 result event, opt is %+v", opt)
	err := h.JournalService.SendEvent(ctx, &journalentity.MetricsEvent{
		UserID:    opt.UserID,
		MessageID: opt.MessageID,
		EventName: journalentity.SandboxEventNameExecutableCheck,
		EventData: &journalentity.ExecutableCheckResultEvent{
			Identifier:            opt.Identifier,
			Version:               opt.Version,
			IsExecutable:          opt.IsExecutable,
			NotExecutableCategory: opt.NotExecutableCategory,
			NotExecutableReason:   opt.NotExecutableReason,
			CodeType:              string(opt.CodeType),
			Time:                  opt.Time,
			Language:              opt.Language,
			LogID:                 opt.LogID,
		},
	})
	if err != nil {
		logs.CtxError(ctx, "[sendCodeCheckResultEvent] failed to send event: %v", err)
	}
	// 检查成功率/流量
	_ = metrics.CodeAssistMetric.SandboxCodeExecutableThroughput.WithTags(&metrics.CodeAssistSandboxCodeExecutableTag{
		IsExecutable: opt.IsExecutable,
		CodeType:     string(opt.CodeType),
	}).Add(1)
	// 检查耗时
	_ = metrics.CodeAssistMetric.SandboxCodeExecutableLatency.WithTags(&metrics.CodeAssistSandboxCodeExecutableTag{
		IsExecutable: opt.IsExecutable,
		CodeType:     string(opt.CodeType),
	}).Observe(float64(opt.Time))
	// 检查失败原因
	for _, reason := range opt.NotExecutableReason {
		_ = metrics.CodeAssistMetric.SandboxCodeFileNotExecutableThroughput.WithTags(&metrics.CodeAssistSandboxCodeFileNotExecutableTag{
			Language: opt.Language,
			Category: reason.Category,
			CodeType: string(opt.CodeType),
		}).Add(1)
	}
}

// slidingSplit 滑动窗口切分文本
func slidingSplit(text string, window int) []string {
	r := []rune(text)
	l := len(r)
	if l == 0 || window <= 0 || window > l {
		return []string{text}
	}

	step := window / 2
	result := make([]string, 0)
	i := 0
	for ; i < l-window+1; i += step {
		w := r[i : i+window]
		result = append(result, string(w))
	}
	if i < l {
		result = append(result, string(r[i:l]))
	}

	return result
}
